// Telegram WebApp specific styles

// Telegram WebApp viewport
.tg-viewport {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  background-color: $tg-bg-color;
}

// Telegram safe areas
.tg-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

// Telegram theme adaptation
.tg-theme {
  --bg-color: var(--tg-theme-bg-color, #ffffff);
  --text-color: var(--tg-theme-text-color, #000000);
  --hint-color: var(--tg-theme-hint-color, #8e8e93);
  --link-color: var(--tg-theme-link-color, #007aff);
  --button-color: var(--tg-theme-button-color, #007aff);
  --button-text-color: var(--tg-theme-button-text-color, #ffffff);
  --secondary-bg-color: var(--tg-theme-secondary-bg-color, #f2f2f7);

  background-color: var(--bg-color);
  color: var(--text-color);
}

// Telegram main button styles
.tg-main-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 48px;
  background-color: $tg-button-color;
  color: $tg-button-text-color;
  border: none;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  cursor: pointer;
  z-index: $z-index-modal;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.hidden {
    display: none;
  }
}

// Telegram back button
.tg-back-button {
  position: fixed;
  top: $spacing-md;
  left: $spacing-md;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: $z-index-dropdown;

  &::before {
    content: '←';
    font-size: $font-size-lg;
    color: $tg-link-color;
  }

  &.hidden {
    display: none;
  }
}

// Telegram haptic feedback classes
.tg-haptic-light {
  // Will be handled by JavaScript
}

.tg-haptic-medium {
  // Will be handled by JavaScript
}

.tg-haptic-heavy {
  // Will be handled by JavaScript
}

// Telegram-specific responsive adjustments
@media (max-width: 480px) {
  .tg-mobile {
    .toolbar {
      padding: $spacing-xs $spacing-sm;
      flex-wrap: wrap;

      &__group {
        gap: 2px;
      }

      &__button {
        padding: $spacing-xs;
        font-size: $font-size-sm;
        min-width: 32px;
        min-height: 32px;
      }
    }

    .text-editor,
    .sticker-panel {
      padding: $spacing-sm;

      &__header {
        padding: $spacing-sm;

        h3 {
          font-size: $font-size-md;
        }
      }

      &__field {
        margin-bottom: $spacing-sm;

        label {
          font-size: $font-size-xs;
        }

        input,
        textarea,
        select {
          font-size: $font-size-sm;
          padding: $spacing-xs $spacing-sm;
        }
      }

      &__row {
        grid-template-columns: 1fr;
        gap: $spacing-sm;
      }
    }

    .canvas-container {
      border-radius: $border-radius-md;
      margin-bottom: $spacing-sm;
    }

    .modal {
      padding: $spacing-sm;

      &__content {
        max-height: 90vh;
      }

      &__header {
        padding: $spacing-sm;

        h2 {
          font-size: $font-size-md;
        }
      }

      &__body {
        padding: $spacing-sm;
      }
    }

    .template-grid {
      grid-template-columns: 1fr;
      gap: $spacing-sm;
      max-height: 50vh;
    }

    .template-card {
      &__thumbnail {
        height: 100px;
      }

      &__info {
        padding: $spacing-sm;

        h4 {
          font-size: $font-size-sm;
        }

        p {
          font-size: $font-size-xs;
        }
      }
    }
  }
}

// Touch-friendly improvements
@media (hover: none) and (pointer: coarse) {
  .toolbar__button,
  .sticker-panel__item,
  .template-card {
    min-height: 44px;
    min-width: 44px;
  }

  .text-editor__button-group button {
    min-height: 40px;
    padding: $spacing-sm;
  }

  .text-editor__color-picker {
    min-width: 44px;
    min-height: 44px;
  }
}

// Landscape orientation adjustments
@media (max-width: 768px) and (orientation: landscape) {
  .editor-page {
    &__canvas-area {
      height: 40vh;
      min-height: 250px;
    }

    &__panels {
      max-height: 50vh;
      overflow-y: auto;
    }
  }

  .modal__content {
    max-height: 85vh;
  }
}

// Very small screens (iPhone SE, etc.)
@media (max-width: 375px) {
  .toolbar {
    &__group {
      gap: 1px;
    }

    &__button {
      min-width: 28px;
      min-height: 28px;
      padding: 4px;
      font-size: 12px;
    }
  }

  .text-editor,
  .sticker-panel {
    &__header {
      h3 {
        font-size: 14px;
      }
    }

    &__close {
      width: 28px;
      height: 28px;
      font-size: 16px;
    }
  }

  .sticker-panel__grid {
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 4px;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .tg-theme {
    --bg-color: var(--tg-theme-bg-color, #1c1c1e);
    --text-color: var(--tg-theme-text-color, #ffffff);
    --hint-color: var(--tg-theme-hint-color, #8e8e93);
    --secondary-bg-color: var(--tg-theme-secondary-bg-color, #2c2c2e);
  }
}

// Telegram WebApp loading state
.tg-loading {
  @include flex-center;
  @include flex-column;
  height: 100vh;
  gap: $spacing-md;

  &__spinner {
    @extend .spinner;
  }

  &__text {
    color: $tg-hint-color;
    font-size: $font-size-sm;
  }
}
