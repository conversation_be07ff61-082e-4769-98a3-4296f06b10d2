// Telegram WebApp theme colors (primary source)
$tg-bg-color: var(--tg-theme-bg-color, #ffffff);
$tg-text-color: var(--tg-theme-text-color, #000000);
$tg-hint-color: var(--tg-theme-hint-color, #8e8e93);
$tg-link-color: var(--tg-theme-link-color, #007aff);
$tg-button-color: var(--tg-theme-button-color, #007aff);
$tg-button-text-color: var(--tg-theme-button-text-color, #ffffff);
$tg-secondary-bg-color: var(--tg-theme-secondary-bg-color, #f2f2f7);

// Fallback colors (mapped to Telegram variables)
$primary-color: $tg-link-color;
$secondary-color: $tg-button-color;
$success-color: #34c759;
$warning-color: #ff9500;
$error-color: #ff3b30;
$background-color: $tg-bg-color;
$surface-color: $tg-secondary-bg-color;
$text-primary: $tg-text-color;
$text-secondary: $tg-hint-color;
$border-color: $tg-hint-color;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

// Typography
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Z-index
$z-index-dropdown: 1000;
$z-index-modal: 1050;
$z-index-tooltip: 1070;

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
