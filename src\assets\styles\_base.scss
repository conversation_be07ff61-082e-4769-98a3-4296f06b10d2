// Reset and base styles
@use './variables' as *;
@use './mixins' as *;

* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: $font-family-base;
  font-size: $font-size-md;
  line-height: 1.5;
  color: $tg-text-color;
  background-color: $tg-bg-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // Prevent overscroll on mobile
  overscroll-behavior: none;
}

#app {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 $spacing-md 0;
  font-weight: $font-weight-semibold;
  line-height: 1.2;
  color: $tg-text-color;
}

h1 {
  font-size: $font-size-xxxl;
}
h2 {
  font-size: $font-size-xxl;
}
h3 {
  font-size: $font-size-xl;
}
h4 {
  font-size: $font-size-lg;
}
h5 {
  font-size: $font-size-md;
}
h6 {
  font-size: $font-size-sm;
}

p {
  margin: 0 0 $spacing-md 0;
  color: $tg-text-color;
}

a {
  color: $tg-link-color;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// Form elements
input,
textarea,
select,
button {
  font-family: inherit;
  font-size: inherit;
}

input,
textarea {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $tg-hint-color;
  border-radius: $border-radius-md;
  background-color: $tg-secondary-bg-color;
  color: $tg-text-color;

  &:focus {
    outline: none;
    border-color: $tg-link-color;
  }

  &::placeholder {
    color: $tg-hint-color;
  }
}

button {
  @include button-base;
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
