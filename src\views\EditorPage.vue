<template>
  <div class="editor-page">
    <!-- Loading screen -->
    <div v-if="!isReady" class="loading-screen">
      <div class="loading-spinner">
        <div class="spinner-circle"></div>
      </div>
      <p>Загрузка редактора...</p>
    </div>

    <!-- Main editor interface -->
    <div v-else class="editor-content">
      <!-- Top toolbar -->
      <div class="top-toolbar">
        <div class="toolbar-section">
          <button
            class="toolbar-btn"
            :disabled="!canvasStore.canUndo"
            @click="handleUndo"
            title="Отменить"
          >
            <Undo2 :size="20" />
          </button>
          <button
            class="toolbar-btn"
            :disabled="!canvasStore.canRedo"
            @click="handleRedo"
            title="Повторить"
          >
            <Redo2 :size="20" />
          </button>
        </div>

        <div class="toolbar-section">
          <button
            class="toolbar-btn"
            :disabled="!selectedElement"
            @click="handleCopyElement"
            title="Копировать"
          >
            <Copy :size="20" />
          </button>
          <button
            class="toolbar-btn"
            :disabled="!selectedElement"
            @click="handleDeleteElement"
            title="Удалить"
          >
            <Trash2 :size="20" />
          </button>
        </div>

        <div class="toolbar-section">
          <button
            class="toolbar-btn"
            @click="showLayersPanel = !showLayersPanel"
            :class="{ active: showLayersPanel }"
            title="Слои"
          >
            <Layers :size="20" />
          </button>
        </div>
      </div>

      <!-- Canvas container (centered) -->
      <div class="canvas-container">
        <CanvasRenderer
          ref="canvasRenderer"
          :width="canvasStore.width"
          :height="canvasStore.height"
          @element-selected="handleElementSelected"
          @element-moved="handleElementMoved"
          @canvas-updated="handleCanvasUpdated"
        />
      </div>

      <!-- Layers panel -->
      <div v-if="showLayersPanel" class="layers-panel">
        <div class="panel-header">
          <h3>
            <Layers :size="16" />
            Слои
          </h3>
          <button class="close-btn" @click="showLayersPanel = false">
            <X :size="16" />
          </button>
        </div>
        <div class="layers-list">
          <div
            v-for="element in sortedElements"
            :key="element.id"
            class="layer-item"
            :class="{ active: element.id === selectedElement?.id }"
            @click="canvasStore.selectElement(element.id)"
          >
            <div class="layer-info">
              <component :is="getLayerIcon(element.type)" :size="16" class="layer-icon" />
              <span class="layer-name">{{ getLayerName(element) }}</span>
            </div>
            <div class="layer-actions">
              <button
                class="layer-action-btn"
                @click.stop="canvasStore.moveElementToFront(element.id)"
                title="На передний план"
              >
                <ChevronUp :size="14" />
              </button>
              <button
                class="layer-action-btn"
                @click.stop="canvasStore.moveElementToBack(element.id)"
                title="На задний план"
              >
                <ChevronDown :size="14" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom toolbar -->
      <BottomToolbar :canvas-ref="canvasRenderer" @tool-changed="handleToolChanged" />
    </div>

    <!-- Export modal -->
    <ExportModal
      :is-visible="showExportModal"
      @close="handleCloseExportModal"
      @export="handleExport"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import CanvasRenderer from '@/components/CanvasRenderer.vue'
import BottomToolbar from '@/components/BottomToolbar.vue'
import ExportModal from '@/components/ExportModal.vue'
import {
  Undo2,
  Redo2,
  Copy,
  Trash2,
  Layers,
  X,
  ChevronUp,
  ChevronDown,
  Type,
  Image,
  Square,
} from 'lucide-vue-next'

export default {
  name: 'EditorPage',
  components: {
    CanvasRenderer,
    BottomToolbar,
    ExportModal,
    Undo2,
    Redo2,
    Copy,
    Trash2,
    Layers,
    X,
    ChevronUp,
    ChevronDown,
    Type,
    Image,
    Square,
  },
  setup() {
    const canvasStore = useCanvasStore()
    const {
      isReady,
      user,
      showMainButton,
      hideMainButton,
      hapticFeedback,
      showAlert,
      onMainButtonClick,
    } = useTelegram()

    // Refs
    const canvasRenderer = ref(null)

    // UI state
    const showExportModal = ref(false)
    const showLayersPanel = ref(false)

    // Computed
    const selectedElement = computed(() => canvasStore.selectedElement)
    const canvasWidth = computed(() => canvasStore.width)
    const canvasHeight = computed(() => canvasStore.height)
    const sortedElements = computed(() => {
      return [...canvasStore.elements].sort((a, b) => b.zIndex - a.zIndex)
    })

    // Initialize
    onMounted(async () => {
      await nextTick()

      // Set up Telegram main button
      if (isReady.value) {
        showMainButton('Экспорт', {
          color: '#007aff',
          textColor: '#ffffff',
        })
      }

      console.log('Editor initialized for user:', user.value)
      console.log('Canvas dimensions:', canvasWidth.value, 'x', canvasHeight.value)
    })

    // Tool handlers
    const handleToolChanged = (toolId) => {
      console.log('Tool changed:', toolId)
      hapticFeedback.selectionChanged()
    }

    // Top toolbar handlers
    const handleUndo = () => {
      canvasStore.undo()
      hapticFeedback.impactOccurred('light')
    }

    const handleRedo = () => {
      canvasStore.redo()
      hapticFeedback.impactOccurred('light')
    }

    const handleCopyElement = () => {
      if (selectedElement.value) {
        canvasStore.duplicateElement(selectedElement.value.id)
        hapticFeedback.impactOccurred('medium')
      }
    }

    const handleDeleteElement = () => {
      if (selectedElement.value) {
        canvasStore.deleteElement(selectedElement.value.id)
        hapticFeedback.impactOccurred('medium')
      }
    }

    // Layer helpers
    const getLayerIcon = (type) => {
      switch (type) {
        case 'text':
          return 'Type'
        case 'sticker':
        case 'image':
          return 'Image'
        default:
          return 'Square'
      }
    }

    const getLayerName = (element) => {
      switch (element.type) {
        case 'text':
          return element.text || 'Текст'
        case 'sticker':
          return 'Стикер'
        case 'image':
          return 'Изображение'
        default:
          return 'Элемент'
      }
    }

    // Element handlers
    const handleElementSelected = (element) => {
      if (element.type === 'text') {
        showTextEditor.value = true
        activeMode.value = 'text'
      }
    }

    const handleElementMoved = (element) => {
      // Element moved, could save state here
    }

    const handleCanvasUpdated = () => {
      // Canvas updated, could auto-save here
    }

    // Panel handlers
    // Export handlers

    const handleCloseExportModal = () => {
      showExportModal.value = false
    }

    const handleExport = async (exportData) => {
      try {
        // Get canvas data URL
        const dataUrl = canvasRenderer.value?.exportAsDataURL(
          exportData.format === 'jpg' ? 'jpeg' : exportData.format,
          exportData.quality,
        )

        if (!dataUrl) {
          throw new Error('Failed to export canvas')
        }

        // Create download link
        const link = document.createElement('a')
        link.download = `framory-design.${exportData.format}`
        link.href = dataUrl
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Send to Telegram if requested
        if (exportData.sendToTelegram && window.Telegram?.WebApp) {
          // Send via Telegram WebApp
          window.Telegram.WebApp.sendData(
            JSON.stringify({
              type: 'photo',
              dataUrl: dataUrl,
              caption: exportData.telegramCaption || 'Создано в Framory',
            }),
          )
        }

        showAlert('Изображение экспортировано!')
        hapticFeedback.notificationOccurred('success')
      } catch (error) {
        console.error('Export failed:', error)
        showAlert('Ошибка при экспорте изображения')
        hapticFeedback.notificationOccurred('error')
      }
    }

    // Main button export handler
    const handleMainButtonExport = async () => {
      try {
        if (!canvasRenderer.value?.canvasRef) {
          throw new Error('Canvas not found')
        }

        const dataUrl = canvasRenderer.value.canvasRef.toDataURL('image/png', 0.9)

        // Convert to blob
        const response = await fetch(dataUrl)
        const blob = await response.blob()

        // Create download link
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `framory-design-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        hapticFeedback.notificationOccurred('success')
      } catch (error) {
        console.error('Export failed:', error)
        hapticFeedback.notificationOccurred('error')
      }
    }

    // Setup main button
    onMounted(() => {
      const unsubscribe = onMainButtonClick(handleMainButtonExport)

      // Cleanup on unmount
      onUnmounted(() => {
        unsubscribe()
      })
    })

    return {
      // State
      isReady,
      canvasRenderer,
      showExportModal,
      showLayersPanel,
      canvasWidth,
      canvasHeight,
      selectedElement,
      sortedElements,
      canvasStore,

      // Handlers
      handleToolChanged,
      handleElementSelected,
      handleElementMoved,
      handleCanvasUpdated,
      handleExport,
      handleCloseExportModal,
      handleUndo,
      handleRedo,
      handleCopyElement,
      handleDeleteElement,
      getLayerIcon,
      getLayerName,
    }
  },
}
</script>

<style lang="scss" scoped>
.editor-page {
  min-height: 100vh;
  background: var(--tg-theme-bg-color, #ffffff);
  display: flex;
  flex-direction: column;
  position: relative;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.top-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--tg-theme-secondary-bg-color, #f2f2f7);
  border-bottom: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  position: sticky;
  top: 0;
  z-index: 10;

  .toolbar-section {
    display: flex;
    gap: 8px;
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: var(--tg-theme-bg-color, #ffffff);
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    &.active {
      background: var(--tg-theme-button-color, #007aff);
      color: var(--tg-theme-button-text-color, #ffffff);
    }
  }

  @media (max-width: 768px) {
    padding: 8px 12px;

    .toolbar-btn {
      width: 36px;
      height: 36px;
    }
  }
}

.canvas-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  padding-bottom: 100px; // Space for bottom toolbar
  overflow: hidden; // Changed from auto to hidden for better scaling
  position: relative;

  // Center the canvas with proper spacing
  height: calc(100vh - 180px); // Account for top toolbar
  min-height: 300px; // Minimum height for very small screens

  @media (max-width: 768px) {
    padding: 12px;
    padding-bottom: 100px;
    height: calc(100vh - 160px);
    min-height: 250px;
  }

  // Ensure the canvas renderer takes full container size
  > * {
    width: 100%;
    height: 100%;
  }
}

.layers-panel {
  position: fixed;
  top: 60px;
  right: 16px;
  width: 280px;
  max-height: calc(100vh - 180px);
  background: var(--tg-theme-secondary-bg-color, #f2f2f7);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 20;
  overflow: hidden;
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
    background: var(--tg-theme-bg-color, #ffffff);

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--tg-theme-text-color, #000000);
    }

    .close-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 6px;
      background: transparent;
      color: var(--tg-theme-hint-color, #8e8e93);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
        color: var(--tg-theme-text-color, #000000);
      }
    }
  }

  .layers-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
  }

  .layer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;

    &:hover {
      background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
    }

    &.active {
      background: var(--tg-theme-button-color, #007aff);
      color: var(--tg-theme-button-text-color, #ffffff);

      .layer-icon,
      .layer-name {
        color: inherit;
      }
    }

    .layer-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .layer-icon {
        color: var(--tg-theme-hint-color, #8e8e93);
      }

      .layer-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--tg-theme-text-color, #000000);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .layer-actions {
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover .layer-actions {
      opacity: 1;
    }

    .layer-action-btn {
      width: 24px;
      height: 24px;
      border: none;
      border-radius: 4px;
      background: transparent;
      color: var(--tg-theme-hint-color, #8e8e93);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
        color: var(--tg-theme-text-color, #000000);
      }
    }
  }

  @media (max-width: 768px) {
    top: 52px;
    right: 8px;
    width: 260px;
    max-height: calc(100vh - 140px);
  }
}

.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: var(--tg-theme-hint-color, #8e8e93);

  p {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 500;
  }
}

.loading-spinner {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-circle {
  width: 32px;
  height: 32px;
  border: 3px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  border-top: 3px solid var(--tg-theme-button-color, #007aff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
