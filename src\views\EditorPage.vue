<template>
  <div class="editor-page">
    <!-- Loading screen -->
    <div v-if="!isReady" class="loading-screen">
      <div class="loading-spinner">
        <div class="spinner-circle"></div>
      </div>
      <p>Загрузка редактора...</p>
    </div>

    <!-- Main editor interface -->
    <div v-else class="editor-content">
      <!-- Canvas container (centered) -->
      <div class="canvas-container">
        <CanvasRenderer
          ref="canvasRenderer"
          :width="canvasStore.width"
          :height="canvasStore.height"
          @element-selected="handleElementSelected"
          @element-moved="handleElementMoved"
          @canvas-updated="handleCanvasUpdated"
        />
      </div>

      <!-- Bottom toolbar -->
      <BottomToolbar :canvas-ref="canvasRenderer" @tool-changed="handleToolChanged" />
    </div>

    <!-- Export modal -->
    <ExportModal
      :is-visible="showExportModal"
      @close="handleCloseExportModal"
      @export="handleExport"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import CanvasRenderer from '@/components/CanvasRenderer.vue'
import BottomToolbar from '@/components/BottomToolbar.vue'
import ExportModal from '@/components/ExportModal.vue'

export default {
  name: 'EditorPage',
  components: {
    CanvasRenderer,
    BottomToolbar,
    ExportModal,
  },
  setup() {
    const canvasStore = useCanvasStore()
    const {
      isReady,
      user,
      showMainButton,
      hideMainButton,
      hapticFeedback,
      showAlert,
      onMainButtonClick,
    } = useTelegram()

    // Refs
    const canvasRenderer = ref(null)

    // UI state
    const showExportModal = ref(false)

    // Canvas dimensions (optimized for mobile)
    const canvasWidth = ref(800)
    const canvasHeight = ref(600)

    // Computed
    const selectedElement = computed(() => canvasStore.selectedElement)

    // Initialize
    onMounted(async () => {
      await nextTick()

      // Set up Telegram main button
      if (isReady.value) {
        showMainButton('Экспорт', {
          color: '#007aff',
          textColor: '#ffffff',
        })
      }

      // Adjust canvas size for mobile
      adjustCanvasSize()

      console.log('Editor initialized for user:', user.value)
    })

    // Adjust canvas size based on viewport
    const adjustCanvasSize = () => {
      const isMobile = window.innerWidth < 768
      if (isMobile) {
        canvasWidth.value = 600
        canvasHeight.value = 450
      } else {
        canvasWidth.value = 800
        canvasHeight.value = 600
      }
    }

    // Tool handlers
    const handleToolChanged = (toolId) => {
      console.log('Tool changed:', toolId)
      hapticFeedback.selectionChanged()
    }

    // Element handlers
    const handleElementSelected = (element) => {
      if (element.type === 'text') {
        showTextEditor.value = true
        activeMode.value = 'text'
      }
    }

    const handleElementMoved = (element) => {
      // Element moved, could save state here
    }

    const handleCanvasUpdated = () => {
      // Canvas updated, could auto-save here
    }

    // Panel handlers
    // Export handlers

    const handleCloseExportModal = () => {
      showExportModal.value = false
    }

    const handleExport = async (exportData) => {
      try {
        // Get canvas data URL
        const dataUrl = canvasRenderer.value?.exportAsDataURL(
          exportData.format === 'jpg' ? 'jpeg' : exportData.format,
          exportData.quality,
        )

        if (!dataUrl) {
          throw new Error('Failed to export canvas')
        }

        // Create download link
        const link = document.createElement('a')
        link.download = `framory-design.${exportData.format}`
        link.href = dataUrl
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Send to Telegram if requested
        if (exportData.sendToTelegram && window.Telegram?.WebApp) {
          // Send via Telegram WebApp
          window.Telegram.WebApp.sendData(
            JSON.stringify({
              type: 'photo',
              dataUrl: dataUrl,
              caption: exportData.telegramCaption || 'Создано в Framory',
            }),
          )
        }

        showAlert('Изображение экспортировано!')
        hapticFeedback.notificationOccurred('success')
      } catch (error) {
        console.error('Export failed:', error)
        showAlert('Ошибка при экспорте изображения')
        hapticFeedback.notificationOccurred('error')
      }
    }

    // Main button export handler
    const handleMainButtonExport = async () => {
      try {
        if (!canvasRenderer.value?.canvasRef) {
          throw new Error('Canvas not found')
        }

        const dataUrl = canvasRenderer.value.canvasRef.toDataURL('image/png', 0.9)

        // Convert to blob
        const response = await fetch(dataUrl)
        const blob = await response.blob()

        // Create download link
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `framory-design-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        hapticFeedback.notificationOccurred('success')
      } catch (error) {
        console.error('Export failed:', error)
        hapticFeedback.notificationOccurred('error')
      }
    }

    // Setup main button
    onMounted(() => {
      const unsubscribe = onMainButtonClick(handleMainButtonExport)

      // Cleanup on unmount
      onUnmounted(() => {
        unsubscribe()
      })
    })

    return {
      // State
      isReady,
      canvasRenderer,
      showExportModal,
      canvasWidth,
      canvasHeight,
      selectedElement,

      // Handlers
      handleToolChanged,
      handleElementSelected,
      handleElementMoved,
      handleCanvasUpdated,
      handleExport,
      handleCloseExportModal,
    }
  },
}
</script>

<style lang="scss" scoped>
.editor-page {
  min-height: 100vh;
  background: var(--tg-theme-bg-color, #ffffff);
  display: flex;
  flex-direction: column;
  position: relative;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.canvas-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  padding-bottom: 100px; // Space for bottom toolbar
  overflow: auto;

  // Center the canvas with proper spacing
  min-height: calc(100vh - 120px);

  @media (max-width: 768px) {
    padding: 12px;
    padding-bottom: 100px;
  }
}

.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: #64748b;

  p {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 500;
  }
}

.loading-spinner {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-circle {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
