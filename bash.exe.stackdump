Stack trace:
Frame         Function      Args
0007FFFF7A00  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF6900) msys-2.0.dll+0x2118E
0007FFFF7A00  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF7CD8) msys-2.0.dll+0x69BA
0007FFFF7A00  0002100469F2 (00021028DF99, 0007FFFF78B8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF7A00  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF7A00  00021006A545 (0007FFFF7A10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF7CE0  00021006B9A5 (0007FFFF7A10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAA1B20000 ntdll.dll
7FFAA0010000 KERNEL32.DLL
7FFA9ED20000 KERNELBASE.dll
7FFAA08B0000 USER32.dll
7FFA9F3A0000 win32u.dll
7FFAA00E0000 GDI32.dll
7FFA9F110000 gdi32full.dll
7FFA9EC70000 msvcp_win.dll
7FFA9F250000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA9FF50000 advapi32.dll
7FFAA1860000 msvcrt.dll
7FFAA1570000 sechost.dll
7FFAA1910000 RPCRT4.dll
7FFA9E270000 CRYPTBASE.DLL
7FFA9F6A0000 bcryptPrimitives.dll
7FFAA0830000 IMM32.DLL
7FFA78F90000 windhawk.dll
7FFA78C20000 shrink-address-bar-height_1.0.1_195656.dll
