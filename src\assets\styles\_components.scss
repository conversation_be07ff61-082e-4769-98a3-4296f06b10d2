// Component-specific styles

// Canvas container
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: $border-radius-lg;
  background-color: $tg-secondary-bg-color;
  box-shadow: $shadow-md;

  canvas {
    display: block;
    max-width: 100%;
    max-height: 100%;
    cursor: crosshair;

    &.dragging {
      cursor: grabbing;
    }
  }
}

// Toolbar
.toolbar {
  @include flex-between;
  padding: $spacing-sm $spacing-md;
  background-color: $tg-secondary-bg-color;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-md;

  &__group {
    @include flex-center;
    gap: $spacing-sm;
  }

  &__button {
    @include button-secondary;
    @include touch-target;
    padding: $spacing-sm;
    border-radius: $border-radius-md;

    &--active {
      background-color: $tg-button-color;
      color: $tg-button-text-color;
      border-color: $tg-button-color;
    }
  }
}

// Text editor panel
.text-editor {
  @include card;
  margin-bottom: $spacing-md;

  &__header {
    @include flex-between;
    margin-bottom: $spacing-md;

    h3 {
      margin: 0;
      font-size: $font-size-lg;
    }
  }

  &__field {
    margin-bottom: $spacing-md;

    label {
      display: block;
      margin-bottom: $spacing-xs;
      font-weight: $font-weight-medium;
      color: $tg-text-color;
    }
  }

  &__controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: $spacing-sm;
    margin-top: $spacing-md;
  }

  &__color-picker {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: $border-radius-md;
    cursor: pointer;

    &::-webkit-color-swatch-wrapper {
      padding: 0;
    }

    &::-webkit-color-swatch {
      border: none;
      border-radius: $border-radius-sm;
    }
  }
}

// Sticker panel
.sticker-panel {
  @include card;
  margin-bottom: $spacing-md;

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: $spacing-sm;
    margin-top: $spacing-md;
  }

  &__item {
    @include flex-center;
    @include touch-target;
    aspect-ratio: 1;
    border: 1px solid $tg-hint-color;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: $tg-link-color;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// Loading spinner
.spinner {
  @include flex-center;
  width: 40px;
  height: 40px;

  &__circle {
    width: 24px;
    height: 24px;
    border: 2px solid $tg-hint-color;
    border-top-color: $tg-link-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Modal
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: $z-index-modal;
  @include flex-center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: $spacing-md;

  &__content {
    @include card;
    width: 100%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
  }

  &__header {
    @include flex-between;
    margin-bottom: $spacing-md;

    h2 {
      margin: 0;
    }
  }

  &__close {
    @include button-secondary;
    padding: $spacing-xs;
    border: none;
    background: none;
    font-size: $font-size-lg;
    color: $tg-hint-color;
  }
}
