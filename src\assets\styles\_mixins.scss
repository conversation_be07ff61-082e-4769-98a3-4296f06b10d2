// Mixins
@use './variables' as *;

// Flexbox mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Button mixins
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $border-radius-md;
  font-family: $font-family-base;
  font-size: $font-size-md;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $tg-button-color;
  color: $tg-button-text-color;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }

  &:active:not(:disabled) {
    transform: scale(0.98);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: transparent;
  color: $tg-text-color;
  border: 1px solid $tg-hint-color;

  &:hover:not(:disabled) {
    background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
  }
}

// Card mixin
@mixin card {
  background-color: $tg-secondary-bg-color;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  padding: $spacing-md;
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
}

// Responsive mixins
@mixin mobile-only {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: $breakpoint-md) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// Touch-friendly mixins
@mixin touch-target {
  min-height: 44px;
  min-width: 44px;
}

// Text truncation
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-truncate-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
