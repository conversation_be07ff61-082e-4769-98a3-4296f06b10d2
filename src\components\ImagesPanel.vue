<template>
  <div class="images-panel">
    <div class="upload-section">
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        multiple
        @change="handleFileUpload"
        style="display: none"
      />

      <button class="upload-btn" @click="triggerFileUpload">
        <Upload :size="24" />
        <span>Загрузить изображение</span>
      </button>
    </div>

    <div class="stock-images" v-if="stockImages.length > 0">
      <h4>Стоковые изображения</h4>
      <div class="images-grid">
        <button
          v-for="image in stockImages"
          :key="image.id"
          class="image-btn"
          @click="addImage(image.url)"
        >
          <img :src="image.thumbnail" :alt="image.alt" />
        </button>
      </div>
    </div>

    <div class="recent-images" v-if="recentImages.length > 0">
      <h4>Недавние изображения</h4>
      <div class="images-grid">
        <div
          v-for="image in recentImages"
          :key="image.id"
          class="image-item"
          @click="addImage(image.url)"
        >
          <img :src="image.url" :alt="image.name" />
          <button class="remove-btn" @click.stop="removeRecentImage(image.id)">
            <X :size="14" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import { Upload, X } from 'lucide-vue-next'

export default {
  name: 'ImagesPanel',
  components: {
    Upload,
    X,
  },
  emits: ['close'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    const fileInput = ref(null)
    const recentImages = ref([])
    const stockImages = ref([])

    // Load recent images from localStorage
    const loadRecentImages = () => {
      try {
        const saved = localStorage.getItem('framory-recent-images')
        if (saved) {
          recentImages.value = JSON.parse(saved)
        }
      } catch (error) {
        console.error('Failed to load recent images:', error)
      }
    }

    // Save recent images to localStorage
    const saveRecentImages = () => {
      try {
        localStorage.setItem('framory-recent-images', JSON.stringify(recentImages.value))
      } catch (error) {
        console.error('Failed to save recent images:', error)
      }
    }

    // Add image to recent list
    const addToRecent = (url, name) => {
      const image = {
        id: Date.now().toString(),
        url,
        name,
        timestamp: Date.now(),
      }

      // Remove if already exists
      recentImages.value = recentImages.value.filter((img) => img.url !== url)

      // Add to beginning
      recentImages.value.unshift(image)

      // Keep only last 20 images
      if (recentImages.value.length > 20) {
        recentImages.value = recentImages.value.slice(0, 20)
      }

      saveRecentImages()
    }

    const triggerFileUpload = () => {
      fileInput.value?.click()
    }

    const handleFileUpload = (event) => {
      const files = Array.from(event.target.files)

      files.forEach((file) => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onload = (e) => {
            const imageUrl = e.target.result
            addImage(imageUrl, file.name)
            addToRecent(imageUrl, file.name)
          }
          reader.readAsDataURL(file)
        }
      })

      // Reset input
      event.target.value = ''
    }

    const addImage = (url, name = 'Image') => {
      const element = {
        type: 'image',
        src: url,
        x: 100,
        y: 100,
        width: 200,
        height: 200,
        opacity: 1,
        rotation: 0,
        filters: {
          brightness: 100,
          contrast: 100,
          saturation: 100,
          blur: 0,
        },
      }

      canvasStore.addElement(element)
      hapticFeedback.impactOccurred('medium')
      emit('close')
    }

    const removeRecentImage = (imageId) => {
      recentImages.value = recentImages.value.filter((img) => img.id !== imageId)
      saveRecentImages()
      hapticFeedback.impactOccurred('light')
    }

    // Load stock images (placeholder)
    const loadStockImages = () => {
      stockImages.value = [
        {
          id: 'stock-1',
          url: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400',
          thumbnail: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=200',
          alt: 'Abstract background',
        },
        {
          id: 'stock-2',
          url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
          thumbnail: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200',
          alt: 'Gradient background',
        },
      ]
    }

    onMounted(() => {
      loadRecentImages()
      loadStockImages()
    })

    return {
      fileInput,
      recentImages,
      stockImages,
      triggerFileUpload,
      handleFileUpload,
      addImage,
      removeRecentImage,
    }
  },
}
</script>

<style lang="scss" scoped>
.images-panel {
  padding: 20px;
}

.upload-section {
  margin-bottom: 24px;
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 16px;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    color: #667eea;
  }

  span {
    font-size: 14px;
    font-weight: 500;
  }
}

.stock-images,
.recent-images {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 12px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;

  &:hover {
    border-color: #667eea;
    transform: scale(1.05);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    &:hover {
      background: #dc2626;
    }
  }

  &:hover .remove-btn {
    opacity: 1;
  }
}
</style>
