import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useCanvasStore } from '../canvas'

// Mock canvas context
const mockContext = {
  font: '',
  measureText: vi.fn().mockReturnValue({
    width: 100,
    actualBoundingBoxAscent: 20,
    actualBoundingBoxDescent: 5,
  }),
}

// Mock canvas element
global.HTMLCanvasElement.prototype.getContext = vi.fn().mockReturnValue(mockContext)

describe('Canvas Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const store = useCanvasStore()

    expect(store.width).toBe(800)
    expect(store.height).toBe(600)
    expect(store.elements).toEqual([])
    expect(store.selectedElement).toBeNull()
    expect(store.canUndo).toBe(false)
    expect(store.canRedo).toBe(false)
  })

  it('should initialize canvas with configuration', () => {
    const store = useCanvasStore()
    const config = {
      width: 1200,
      height: 800,
      background: '#ff0000',
    }

    store.initializeCanvas(config)

    expect(store.width).toBe(1200)
    expect(store.height).toBe(800)
    expect(store.background.value).toBe('#ff0000')
    expect(store.background.type).toBe('color')
  })

  it('should handle transparent background', () => {
    const store = useCanvasStore()
    const config = {
      width: 800,
      height: 600,
      background: 'transparent',
    }

    store.initializeCanvas(config)

    expect(store.background.type).toBe('transparent')
    expect(store.background.value).toBe('transparent')
    expect(store.background.opacity).toBe(0)
  })

  it('should add text element', () => {
    const store = useCanvasStore()

    const element = store.addTextElement({
      text: 'Test Text',
      x: 100,
      y: 100,
      fontSize: 24,
      color: '#000000',
    })

    expect(store.elements).toHaveLength(1)
    expect(element.text).toBe('Test Text')
    expect(element.type).toBe('text')
    expect(element.x).toBe(100)
    expect(element.y).toBe(100)
  })

  it('should select and deselect elements', () => {
    const store = useCanvasStore()

    const element = store.addTextElement({ text: 'Test' })

    store.selectElement(element.id)
    expect(store.selectedElement).toBe(element)

    store.selectElement(null)
    expect(store.selectedElement).toBeNull()
  })

  it('should delete elements', () => {
    const store = useCanvasStore()

    const element = store.addTextElement({ text: 'Test' })
    expect(store.elements).toHaveLength(1)

    store.deleteElement(element.id)
    expect(store.elements).toHaveLength(0)
  })

  it('should duplicate elements', () => {
    const store = useCanvasStore()

    const element = store.addTextElement({ text: 'Test', x: 100, y: 100 })
    const duplicate = store.duplicateElement(element.id)

    expect(store.elements).toHaveLength(2)
    expect(duplicate.text).toBe('Test')
    expect(duplicate.x).toBe(110) // Should be offset
    expect(duplicate.y).toBe(110)
    expect(duplicate.id).not.toBe(element.id)
  })

  it('should handle undo/redo', () => {
    const store = useCanvasStore()

    // Add element
    const element = store.addTextElement({ text: 'Test' })
    expect(store.elements).toHaveLength(1)
    expect(store.canUndo).toBe(true)

    // Undo
    store.undo()
    expect(store.elements).toHaveLength(0)
    expect(store.canRedo).toBe(true)

    // Redo
    store.redo()
    expect(store.elements).toHaveLength(1)
  })

  it('should move elements to front/back', () => {
    const store = useCanvasStore()

    const element1 = store.addTextElement({ text: 'First' })
    const element2 = store.addTextElement({ text: 'Second' })

    const initialZIndex1 = element1.zIndex
    const initialZIndex2 = element2.zIndex

    // Move first element to front
    store.moveElementToFront(element1.id)
    expect(element1.zIndex).toBeGreaterThan(initialZIndex2)

    // Move second element to back
    store.moveElementToBack(element2.id)
    expect(element2.zIndex).toBeLessThan(initialZIndex1)
  })

  it('should update element properties', () => {
    const store = useCanvasStore()

    const element = store.addTextElement({ text: 'Test', fontSize: 16 })

    store.updateElement(element.id, { fontSize: 24, color: '#ff0000' })

    expect(element.fontSize).toBe(24)
    expect(element.color).toBe('#ff0000')
    expect(element.text).toBe('Test') // Should preserve other properties
  })
})
