{"app": {"title": "Framory", "loading": "Loading...", "error": "An error occurred", "retry": "Retry"}, "editor": {"title": "Post Editor", "loading": "Loading editor...", "canvas": {"title": "<PERSON><PERSON>", "empty": "Canvas is empty. Add text or sticker."}}, "toolbar": {"undo": "Undo", "redo": "Redo", "addText": "Add Text", "addSticker": "<PERSON><PERSON>", "background": "Background Settings", "duplicate": "Duplicate", "delete": "Delete", "moveToFront": "Bring to Front", "moveToBack": "Send to Back", "clear": "<PERSON>vas", "saveTemplate": "Save as Template", "export": "Export"}, "textEditor": {"title": "Text Editing", "text": "Text", "textPlaceholder": "Enter text...", "font": "Font", "fontSize": "Size", "alignment": "Alignment", "style": "Style", "color": "Text Color", "stroke": {"title": "Stroke", "color": "Stroke Color", "width": "<PERSON><PERSON><PERSON>"}, "shadow": {"title": "Shadow", "color": "Shadow Color", "blur": "Blur", "offsetX": "Offset X", "offsetY": "Offset Y"}, "align": {"left": "Left", "center": "Center", "right": "Right"}}, "stickerPanel": {"title": "Stickers and Emoji", "tabs": {"emoji": "<PERSON><PERSON><PERSON>", "shapes": "<PERSON><PERSON><PERSON>", "upload": "Upload"}, "upload": {"title": "Click to upload image", "subtitle": "PNG, JPG, GIF up to 5MB", "recent": "Recent uploads", "error": {"size": "File too large. Maximum size: 5MB", "type": "Please select an image"}}, "shapes": {"circle": "Circle", "square": "Square", "triangle": "Triangle", "star": "Star", "heart": "Heart", "diamond": "Diamond"}}, "templates": {"save": {"title": "Save Template", "name": "Template Name", "namePlaceholder": "Enter name...", "description": "Description (optional)", "descriptionPlaceholder": "Brief template description...", "tags": "Tags (comma separated)", "tagsPlaceholder": "quote, motivation, business...", "save": "Save", "cancel": "Cancel", "success": "Template \"{name}\" saved!"}, "load": {"title": "Load Template", "search": "Search templates...", "tabs": {"all": "All ({count})", "user": "My ({count})", "default": "Default ({count})"}, "empty": {"search": "Try changing your search query", "user": "You don't have any saved templates yet", "default": "Default templates are not available", "general": "No templates found"}, "delete": {"confirm": "Delete template \"{name}\"? This action cannot be undone.", "success": "Template deleted"}, "success": "Template \"{name}\" loaded!"}, "defaults": {"quote": {"name": "Simple Quote", "description": "Minimalist template for quotes", "text": "Your quote here"}, "announcement": {"name": "Announcement", "description": "Bright template for announcements", "title": "IMPORTANT!", "text": "Your announcement text"}}}, "export": {"title": "Export Image", "format": "Format", "quality": "Quality", "size": "Size", "download": "Download", "share": "Share", "success": "Image exported!", "error": "Error exporting image"}, "background": {"title": "Background Settings", "type": "Background Type", "types": {"color": "Color", "gradient": "Gradient", "image": "Image"}, "color": "Background Color", "gradient": {"colors": "Gradient Colors", "addColor": "Add Color", "removeColor": "Remove Color"}, "image": {"upload": "Upload Image", "fit": "Fit", "fits": {"cover": "Cover", "contain": "Contain", "stretch": "<PERSON><PERSON><PERSON>"}}}, "common": {"close": "Close", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "copy": "Copy", "paste": "Paste", "yes": "Yes", "no": "No", "ok": "OK", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "messages": {"deleteElement": "Delete selected element?", "clearCanvas": "Clear entire canvas? This action cannot be undone.", "unsavedChanges": "You have unsaved changes. Continue?", "templateSaved": "Template saved!", "templateLoaded": "Template loaded!", "exportComplete": "Image exported!", "copySuccess": "Copied to clipboard", "copyError": "Failed to copy"}, "errors": {"loadTemplate": "Error loading template", "saveTemplate": "Error saving template", "deleteTemplate": "Error deleting template", "exportImage": "Error exporting image", "uploadFile": "Error uploading file", "invalidFile": "Invalid file format", "fileTooLarge": "File too large", "networkError": "Network error", "unknownError": "Unknown error"}}