import TelegramBot from 'node-telegram-bot-api'
import { createCanvas, loadImage, registerFont } from 'canvas'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// Load environment variables
dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Bot configuration
const BOT_TOKEN = process.env.BOT_TOKEN
const WEBAPP_URL = process.env.WEBAPP_URL || 'https://your-domain.vercel.app'

if (!BOT_TOKEN) {
  console.error('BOT_TOKEN is required in .env file')
  process.exit(1)
}

// Create bot instance
const bot = new TelegramBot(BOT_TOKEN, { polling: true })

// Quote templates
const QUOTE_TEMPLATES = [
  {
    id: 'gradient-blue',
    name: 'Синий градиент',
    background: { type: 'gradient', colors: ['#667eea', '#764ba2'] },
    textStyle: {
      fontSize: 36,
      fontFamily: 'Georgia',
      color: '#ffffff',
      textAlign: 'center',
      shadow: { color: 'rgba(0,0,0,0.3)', blur: 4, offsetX: 2, offsetY: 2 },
    },
  },
  {
    id: 'gradient-orange',
    name: 'Оранжевый градиент',
    background: { type: 'gradient', colors: ['#ff9a56', '#ff6b6b'] },
    textStyle: {
      fontSize: 32,
      fontFamily: 'Arial',
      color: '#ffffff',
      textAlign: 'center',
      shadow: { color: 'rgba(0,0,0,0.5)', blur: 3, offsetX: 1, offsetY: 1 },
    },
  },
  {
    id: 'solid-dark',
    name: 'Темный фон',
    background: { type: 'color', color: '#2c3e50' },
    textStyle: {
      fontSize: 34,
      fontFamily: 'Georgia',
      color: '#ecf0f1',
      textAlign: 'center',
      shadow: { color: 'rgba(0,0,0,0.7)', blur: 2, offsetX: 1, offsetY: 1 },
    },
  },
  {
    id: 'minimal-light',
    name: 'Светлый минимализм',
    background: { type: 'color', color: '#f8f9fa' },
    textStyle: {
      fontSize: 38,
      fontFamily: 'Arial',
      color: '#2c3e50',
      textAlign: 'center',
      shadow: { color: 'rgba(0,0,0,0.1)', blur: 1, offsetX: 0, offsetY: 1 },
    },
  },
]

// Generate quote image
async function generateQuoteImage(text, template) {
  const width = 800
  const height = 600
  const canvas = createCanvas(width, height)
  const ctx = canvas.getContext('2d')

  // Draw background
  if (template.background.type === 'gradient') {
    const gradient = ctx.createLinearGradient(0, 0, width, height)
    template.background.colors.forEach((color, index) => {
      gradient.addColorStop(index / (template.background.colors.length - 1), color)
    })
    ctx.fillStyle = gradient
  } else {
    ctx.fillStyle = template.background.color
  }
  ctx.fillRect(0, 0, width, height)

  // Setup text style
  const style = template.textStyle
  ctx.font = `${style.fontSize}px ${style.fontFamily}`
  ctx.textAlign = style.textAlign
  ctx.textBaseline = 'middle'

  // Add shadow if specified
  if (style.shadow) {
    ctx.shadowColor = style.shadow.color
    ctx.shadowBlur = style.shadow.blur
    ctx.shadowOffsetX = style.shadow.offsetX
    ctx.shadowOffsetY = style.shadow.offsetY
  }

  // Word wrap function
  function wrapText(text, maxWidth) {
    const words = text.split(' ')
    const lines = []
    let currentLine = words[0]

    for (let i = 1; i < words.length; i++) {
      const word = words[i]
      const width = ctx.measureText(currentLine + ' ' + word).width
      if (width < maxWidth) {
        currentLine += ' ' + word
      } else {
        lines.push(currentLine)
        currentLine = word
      }
    }
    lines.push(currentLine)
    return lines
  }

  // Draw text with word wrapping
  const maxWidth = width - 100 // 50px padding on each side
  const lines = wrapText(text, maxWidth)
  const lineHeight = style.fontSize * 1.2
  const totalHeight = lines.length * lineHeight
  const startY = (height - totalHeight) / 2 + lineHeight / 2

  ctx.fillStyle = style.color
  lines.forEach((line, index) => {
    const y = startY + index * lineHeight
    ctx.fillText(line, width / 2, y)
  })

  // Reset shadow
  ctx.shadowColor = 'transparent'
  ctx.shadowBlur = 0

  return canvas.toBuffer('image/png')
}

// Handle inline queries
bot.on('inline_query', async (query) => {
  const queryText = query.query.trim()

  if (!queryText) {
    // Show help message when query is empty
    const results = [
      {
        type: 'article',
        id: 'help',
        title: '💡 Как использовать бота',
        description: 'Введите текст цитаты для создания красивого поста',
        input_message_content: {
          message_text:
            '🎨 *Генератор постов*\n\nДля создания поста с цитатой:\n1. Введите `@вашбот "Ваша цитата"`\n2. Выберите понравившийся шаблон\n3. Отправьте в чат!\n\n🔗 Для расширенного редактирования используйте [WebApp](' +
            WEBAPP_URL +
            ')',
          parse_mode: 'Markdown',
        },
      },
    ]

    return bot.answerInlineQuery(query.id, results, {
      cache_time: 300,
      switch_pm_text: '🎨 Открыть редактор',
      switch_pm_parameter: 'start',
    })
  }

  try {
    // Generate images for each template
    const results = await Promise.all(
      QUOTE_TEMPLATES.map(async (template, index) => {
        const imageBuffer = await generateQuoteImage(queryText, template)

        return {
          type: 'photo',
          id: `quote_${template.id}_${Date.now()}_${index}`,
          photo_url: 'data:image/png;base64,' + imageBuffer.toString('base64'),
          thumb_url: 'data:image/png;base64,' + imageBuffer.toString('base64'),
          title: template.name,
          description: `Цитата в стиле "${template.name}"`,
          caption: `💬 "${queryText}"\n\n🎨 Создано с помощью Framory`,
        }
      }),
    )

    await bot.answerInlineQuery(query.id, results, {
      cache_time: 60,
      switch_pm_text: '🎨 Открыть редактор',
      switch_pm_parameter: 'start',
    })
  } catch (error) {
    console.error('Error generating quote images:', error)

    // Fallback result
    const fallbackResult = [
      {
        type: 'article',
        id: 'error',
        title: '❌ Ошибка создания изображения',
        description: 'Попробуйте еще раз или используйте WebApp',
        input_message_content: {
          message_text: `💬 "${queryText}"\n\n❌ Не удалось создать изображение. Попробуйте использовать [расширенный редактор](${WEBAPP_URL}).`,
          parse_mode: 'Markdown',
        },
      },
    ]

    await bot.answerInlineQuery(query.id, fallbackResult, {
      cache_time: 10,
      switch_pm_text: '🎨 Открыть редактор',
      switch_pm_parameter: 'start',
    })
  }
})

// Handle /start command
bot.onText(/\/start/, (msg) => {
  const chatId = msg.chat.id
  const firstName = msg.from.first_name || 'друг'

  const welcomeMessage = `
🎨 *Добро пожаловать в Framory!*

Привет, ${firstName}! Этот бот поможет тебе создавать красивые посты для Telegram.

📱 *Два способа использования:*

1️⃣ *Быстрые цитаты (Inline режим):*
   • В любом чате введи \`@${bot.options.username} "Твоя цитата"\`
   • Выбери понравившийся шаблон
   • Готово! 🎉

2️⃣ *Расширенный редактор:*
   • Нажми кнопку ниже
   • Создавай посты с полной настройкой
   • Добавляй стикеры, меняй фоны и многое другое!

🚀 *Попробуй прямо сейчас!*
  `

  const options = {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          {
            text: '🎨 Открыть редактор',
            web_app: { url: WEBAPP_URL },
          },
        ],
        [
          {
            text: '💡 Попробовать inline режим',
            switch_inline_query_current_chat: 'Пример цитаты',
          },
        ],
        [
          {
            text: '📖 Помощь',
            callback_data: 'help',
          },
          {
            text: '⚙️ Настройки',
            callback_data: 'settings',
          },
        ],
      ],
    },
  }

  bot.sendMessage(chatId, welcomeMessage, options)
})

// Handle callback queries
bot.on('callback_query', (callbackQuery) => {
  const message = callbackQuery.message
  const data = callbackQuery.data

  switch (data) {
    case 'help':
      const helpText = `
📖 *Справка по использованию*

🔸 *Inline режим:*
В любом чате введите \`@${bot.options.username} "текст"\` и выберите шаблон

🔸 *WebApp редактор:*
• Добавляйте текст и стикеры
• Настраивайте шрифты и цвета
• Создавайте градиентные фоны
• Сохраняйте шаблоны
• Экспортируйте в высоком качестве

🔸 *Команды бота:*
/start - Главное меню
/help - Эта справка

❓ *Нужна помощь?*
Напишите @support (замените на ваш контакт)
      `

      bot.editMessageText(helpText, {
        chat_id: message.chat.id,
        message_id: message.message_id,
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [[{ text: '← Назад', callback_data: 'back_to_main' }]],
        },
      })
      break

    case 'settings':
      const settingsText = `
⚙️ *Настройки*

🌐 *Язык интерфейса:*
Автоматически определяется по языку Telegram

🎨 *Качество изображений:*
Высокое (800x600px)

💾 *Сохранение шаблонов:*
Локально в браузере

📱 *Версия:* 1.0.0
      `

      bot.editMessageText(settingsText, {
        chat_id: message.chat.id,
        message_id: message.message_id,
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [[{ text: '← Назад', callback_data: 'back_to_main' }]],
        },
      })
      break

    case 'back_to_main':
      // Restart the bot
      bot.emit('message', { ...message, text: '/start' })
      break
  }

  bot.answerCallbackQuery(callbackQuery.id)
})

// Handle /help command
bot.onText(/\/help/, (msg) => {
  bot.emit('callback_query', {
    message: msg,
    data: 'help',
    id: Date.now().toString(),
  })
})

// Error handling
bot.on('error', (error) => {
  console.error('Bot error:', error)
})

bot.on('polling_error', (error) => {
  console.error('Polling error:', error)
})

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down bot...')
  bot.stopPolling()
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('Shutting down bot...')
  bot.stopPolling()
  process.exit(0)
})

console.log('🤖 Framory Bot started!')
console.log(`📱 WebApp URL: ${WEBAPP_URL}`)
console.log('🔄 Polling for messages...')
