import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useTelegram } from '../useTelegram'

describe('useTelegram', () => {
  beforeEach(() => {
    // Reset global window mock
    global.window = {
      Telegram: {
        WebApp: {
          ready: vi.fn(),
          expand: vi.fn(),
          enableClosingConfirmation: vi.fn(),
          setHeaderColor: vi.fn(),
          setBackgroundColor: vi.fn(),
          onEvent: vi.fn(), // Add missing onEvent method
          viewport: {
            requestFullscreen: vi.fn().mockReturnValue(
              Promise.resolve({
                isAvailable: () => true,
              }),
            ),
            isFullscreen: vi.fn().mockReturnValue(true),
          },
          themeParams: {
            bg_color: '#ffffff',
            text_color: '#000000',
            hint_color: '#8e8e93',
            link_color: '#007aff',
            button_color: '#007aff',
            button_text_color: '#ffffff',
          },
          initDataUnsafe: {
            user: {
              id: 123456789,
              first_name: 'Test',
              last_name: 'User',
              username: 'testuser',
            },
          },
          initData: 'test_init_data',
          colorScheme: 'light',
          viewportHeight: 600,
          isExpanded: true,
          HapticFeedback: {
            impactOccurred: vi.fn(),
            notificationOccurred: vi.fn(),
            selectionChanged: vi.fn(),
          },
        },
      },
      innerHeight: 600,
    }
  })

  it('should initialize with Telegram WebApp data', () => {
    const telegram = useTelegram()

    expect(telegram.isReady.value).toBe(true)
    expect(telegram.user.value).toEqual({
      id: 123456789,
      first_name: 'Test',
      last_name: 'User',
      username: 'testuser',
    })
    expect(telegram.colorScheme.value).toBe('light')
    expect(telegram.viewportHeight.value).toBe(600)
  })

  it('should handle theme parameters', () => {
    const telegram = useTelegram()

    expect(telegram.themeParams.bg_color).toBe('#ffffff')
    expect(telegram.themeParams.text_color).toBe('#000000')
    expect(telegram.themeParams.button_color).toBe('#007aff')
  })

  it('should provide haptic feedback methods', () => {
    const telegram = useTelegram()

    telegram.hapticFeedback.impactOccurred('light')
    telegram.hapticFeedback.notificationOccurred('success')
    telegram.hapticFeedback.selectionChanged()

    expect(window.Telegram.WebApp.HapticFeedback.impactOccurred).toHaveBeenCalledWith('light')
    expect(window.Telegram.WebApp.HapticFeedback.notificationOccurred).toHaveBeenCalledWith(
      'success',
    )
    expect(window.Telegram.WebApp.HapticFeedback.selectionChanged).toHaveBeenCalled()
  })

  it('should handle fullscreen mode', () => {
    const telegram = useTelegram()

    expect(telegram.isFullscreen.value).toBe(true)
    expect(window.Telegram.WebApp.viewport.requestFullscreen).toHaveBeenCalled()
  })

  it('should fallback to development mode when Telegram is not available', () => {
    // Remove Telegram from window
    global.window = {}

    const telegram = useTelegram()

    expect(telegram.isReady.value).toBe(true)
    expect(telegram.user.value).toEqual({
      id: 123456789,
      first_name: 'Test',
      last_name: 'User',
      username: 'testuser',
      language_code: 'en',
    })
  })

  it('should handle viewport changes', () => {
    const telegram = useTelegram()

    // Simulate viewport change
    telegram.viewportHeight.value = 800

    expect(telegram.viewportHeight.value).toBe(800)
  })

  it('should handle color scheme changes', () => {
    const telegram = useTelegram()

    // Simulate color scheme change
    telegram.colorScheme.value = 'dark'

    expect(telegram.colorScheme.value).toBe('dark')
  })

  it('should call WebApp initialization methods', () => {
    useTelegram()

    expect(window.Telegram.WebApp.ready).toHaveBeenCalled()
    expect(window.Telegram.WebApp.expand).toHaveBeenCalled()
    expect(window.Telegram.WebApp.enableClosingConfirmation).toHaveBeenCalled()
  })

  it('should set header and background colors', () => {
    useTelegram()

    expect(window.Telegram.WebApp.setHeaderColor).toHaveBeenCalledWith('#ffffff')
    expect(window.Telegram.WebApp.setBackgroundColor).toHaveBeenCalledWith('#ffffff')
  })
})
