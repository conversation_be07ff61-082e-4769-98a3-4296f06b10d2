import { ref, reactive, onMounted } from 'vue'

// Telegram WebApp instance
let tg = null

// Reactive state
const isReady = ref(false)
const user = ref(null)
const initData = ref(null)
const colorScheme = ref('light')
const themeParams = reactive({})
const isFullscreen = ref(false)

// Event handlers
const mainButtonClickHandlers = []
const viewportHeight = ref(window.innerHeight)
const isExpanded = ref(false)

// Main button state
const mainButton = reactive({
  isVisible: false,
  isActive: true,
  text: '',
  color: '',
  textColor: '',
  isProgressVisible: false,
})

// Back button state
const backButton = reactive({
  isVisible: false,
})

// Haptic feedback
const hapticFeedback = {
  impactOccurred: (style = 'medium') => {
    if (tg?.HapticFeedback) {
      tg.HapticFeedback.impactOccurred(style)
    }
  },
  notificationOccurred: (type = 'success') => {
    if (tg?.HapticFeedback) {
      tg.HapticFeedback.notificationOccurred(type)
    }
  },
  selectionChanged: () => {
    if (tg?.HapticFeedback) {
      tg.HapticFeedback.selectionChanged()
    }
  },
}

// Initialize Telegram WebApp
const initTelegram = () => {
  if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
    tg = window.Telegram.WebApp

    // Запрос полноэкранного режима
    if (tg?.viewport?.requestFullscreen?.isAvailable()) {
      tg.viewport
        .requestFullscreen()
        .then(() => {
          isFullscreen.value = tg.viewport.isFullscreen()
          console.log('[Telegram] Fullscreen mode enabled')
        })
        .catch((e) => {
          console.warn('[Telegram] Failed to enable fullscreen:', e)
        })
    }
    // Initialize WebApp
    tg.ready()

    // Set initial state
    isReady.value = true
    user.value = tg.initDataUnsafe?.user || null
    initData.value = tg.initData || null
    colorScheme.value = tg.colorScheme || 'light'
    viewportHeight.value = tg.viewportHeight || window.innerHeight
    isExpanded.value = tg.isExpanded || false

    // Copy theme parameters
    if (tg.themeParams) {
      Object.assign(themeParams, tg.themeParams)
    }

    // Set up event listeners
    setupEventListeners()

    // Expand viewport
    tg.expand()

    // Enable closing confirmation
    tg.enableClosingConfirmation()

    console.log('Telegram WebApp initialized:', {
      user: user.value,
      colorScheme: colorScheme.value,
      themeParams: themeParams,
    })
  } else {
    console.warn('Telegram WebApp not available. Running in development mode.')
    // Set mock data for development
    user.value = {
      id: 123456789,
      first_name: 'Test',
      last_name: 'User',
      username: 'testuser',
      language_code: 'en',
    }
    isReady.value = true
  }
}

// Set up event listeners
const setupEventListeners = () => {
  if (!tg) return

  // Viewport changed
  tg.onEvent('viewportChanged', ({ isStateStable }) => {
    if (isStateStable) {
      viewportHeight.value = tg.viewportHeight
      isExpanded.value = tg.isExpanded
    }
  })

  // Theme changed
  tg.onEvent('themeChanged', () => {
    colorScheme.value = tg.colorScheme
    Object.assign(themeParams, tg.themeParams)
  })

  // Main button clicked
  tg.onEvent('mainButtonClicked', () => {
    mainButtonClickHandlers.forEach((handler) => handler())
  })

  // Back button clicked
  tg.onEvent('backButtonClicked', () => {
    // This will be handled by components
  })
}

// Main button controls
const showMainButton = (text, options = {}) => {
  if (!tg?.MainButton) return

  mainButton.text = text
  mainButton.isVisible = true

  if (options.color) mainButton.color = options.color
  if (options.textColor) mainButton.textColor = options.textColor
  if (typeof options.isActive !== 'undefined') mainButton.isActive = options.isActive

  tg.MainButton.setText(text)
  if (options.color) tg.MainButton.setParams({ color: options.color })
  if (options.textColor) tg.MainButton.setParams({ text_color: options.textColor })

  if (options.isActive !== false) {
    tg.MainButton.enable()
  } else {
    tg.MainButton.disable()
  }

  tg.MainButton.show()
}

const hideMainButton = () => {
  if (!tg?.MainButton) return

  mainButton.isVisible = false
  tg.MainButton.hide()
}

const setMainButtonProgress = (visible) => {
  if (!tg?.MainButton) return

  mainButton.isProgressVisible = visible
  if (visible) {
    tg.MainButton.showProgress()
  } else {
    tg.MainButton.hideProgress()
  }
}

// Back button controls
const showBackButton = () => {
  if (!tg?.BackButton) return

  backButton.isVisible = true
  tg.BackButton.show()
}

const hideBackButton = () => {
  if (!tg?.BackButton) return

  backButton.isVisible = false
  tg.BackButton.hide()
}

// Utility functions
const close = () => {
  if (tg) {
    tg.close()
  } else {
    window.close()
  }
}

const sendData = (data) => {
  if (tg) {
    tg.sendData(JSON.stringify(data))
  } else {
    console.log('Would send data:', data)
  }
}

const openLink = (url, options = {}) => {
  if (tg) {
    tg.openLink(url, options)
  } else {
    window.open(url, '_blank')
  }
}

const openTelegramLink = (url) => {
  if (tg) {
    tg.openTelegramLink(url)
  } else {
    window.open(url, '_blank')
  }
}

const showPopup = (params) => {
  return new Promise((resolve) => {
    if (tg?.showPopup) {
      tg.showPopup(params, resolve)
    } else {
      // Fallback for development
      const result = window.confirm(params.message)
      resolve(result ? 'ok' : 'cancel')
    }
  })
}

const showAlert = (message) => {
  return new Promise((resolve) => {
    if (tg?.showAlert) {
      tg.showAlert(message, resolve)
    } else {
      window.alert(message)
      resolve()
    }
  })
}

const showConfirm = (message) => {
  return new Promise((resolve) => {
    if (tg?.showConfirm) {
      tg.showConfirm(message, resolve)
    } else {
      resolve(window.confirm(message))
    }
  })
}

const requestFullscreen = async () => {
  if (tg?.viewport?.requestFullscreen?.isAvailable()) {
    await tg.viewport.requestFullscreen()
    isFullscreen.value = tg.viewport.isFullscreen()
  }
}

const exitFullscreen = async () => {
  if (tg?.viewport?.exitFullscreen?.isAvailable()) {
    await tg.viewport.exitFullscreen()
    isFullscreen.value = tg.viewport.isFullscreen()
  }
}

// Composable
export function useTelegram() {
  onMounted(() => {
    initTelegram()
  })

  return {
    // State
    isReady,
    user,
    initData,
    colorScheme,
    themeParams,
    viewportHeight,
    isExpanded,
    isFullscreen,
    mainButton,
    backButton,

    // Methods
    hapticFeedback,
    requestFullscreen,
    exitFullscreen,
    showMainButton,
    hideMainButton,
    setMainButtonProgress,
    showBackButton,
    hideBackButton,
    close,
    sendData,
    openLink,
    openTelegramLink,
    showPopup,
    showAlert,
    showConfirm,

    // Raw Telegram object (for advanced usage)
    tg: () => tg,
  }
}
