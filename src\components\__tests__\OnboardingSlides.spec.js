import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { setActivePinia, createPinia } from 'pinia'
import OnboardingSlides from '../OnboardingSlides.vue'

// Mock Telegram WebApp
global.window = {
  Telegram: {
    WebApp: {
      ready: vi.fn(),
      expand: vi.fn(),
      enableClosingConfirmation: vi.fn(),
      setHeaderColor: vi.fn(),
      setBackgroundColor: vi.fn(),
      onEvent: vi.fn(), // Add missing onEvent method
      viewport: {
        requestFullscreen: vi.fn().mockReturnValue(
          Promise.resolve({
            isAvailable: () => false,
          }),
        ),
        isFullscreen: vi.fn().mockReturnValue(false),
      },
      themeParams: {
        bg_color: '#ffffff',
        text_color: '#000000',
        hint_color: '#8e8e93',
        link_color: '#007aff',
        button_color: '#007aff',
        button_text_color: '#ffffff',
      },
      initDataUnsafe: {
        user: {
          id: 123456789,
          first_name: 'Test',
          last_name: 'User',
          username: 'testuser',
        },
      },
      initData: 'test_init_data',
      colorScheme: 'light',
      viewportHeight: 600,
      isExpanded: true,
      HapticFeedback: {
        impactOccurred: vi.fn(),
        notificationOccurred: vi.fn(),
        selectionChanged: vi.fn(),
      },
    },
  },
  innerHeight: 600,
}

describe('OnboardingSlides', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should render welcome slide initially', () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    expect(wrapper.find('h1').text()).toContain('Добро пожаловать')
    expect(wrapper.find('.progress-dot.active')).toBeTruthy()
  })

  it('should navigate between slides', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Click next button
    const nextButton = wrapper.find('.nav-btn--next')
    await nextButton.trigger('click')

    // Should be on project type slide
    expect(wrapper.find('h1').text()).toContain('Выберите тип проекта')
  })

  it('should select project type', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Navigate to project type slide
    const nextButton = wrapper.find('.nav-btn--next')
    await nextButton.trigger('click')

    // Select social media project
    const socialCard = wrapper.find('.project-type-card')
    await socialCard.trigger('click')

    expect(wrapper.vm.selectedProjectType).toBe('social')
  })

  it('should select canvas size', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Navigate to canvas size slide
    await wrapper.vm.goToSlide(2)

    // Select preset size
    const presetCard = wrapper.find('.preset-card')
    await presetCard.trigger('click')

    expect(wrapper.vm.selectedSize).toBeTruthy()
  })

  it('should handle custom canvas size', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Navigate to canvas size slide
    await wrapper.vm.goToSlide(2)

    // Enter custom dimensions
    const widthInput = wrapper.find('input[placeholder="Ширина"]')
    const heightInput = wrapper.find('input[placeholder="Высота"]')

    await widthInput.setValue('1920')
    await heightInput.setValue('1080')

    expect(wrapper.vm.customWidth).toBe(1920)
    expect(wrapper.vm.customHeight).toBe(1080)
  })

  it('should select background', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Navigate to background slide
    await wrapper.vm.goToSlide(3)

    // Select color background
    const colorOption = wrapper.find('.color-option')
    await colorOption.trigger('click')

    expect(wrapper.vm.selectedBackground).toBeTruthy()
  })

  it('should emit create-project event with correct data', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Set up project configuration
    wrapper.vm.selectedProjectType = 'social'
    wrapper.vm.selectedSize = { width: 1080, height: 1080, name: 'Instagram Post' }
    wrapper.vm.selectedBackground = '#ffffff'

    // Navigate to final slide and create project
    await wrapper.vm.goToSlide(3)
    const createButton = wrapper.find('.nav-btn--create')
    await createButton.trigger('click')

    expect(wrapper.emitted('create-project')).toBeTruthy()
    const emittedData = wrapper.emitted('create-project')[0][0]

    expect(emittedData.projectType).toBe('social')
    expect(emittedData.width).toBe(1080)
    expect(emittedData.height).toBe(1080)
    expect(emittedData.background).toBe('#ffffff')
  })

  it('should validate required fields', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Try to go to next slide without selecting project type
    await wrapper.vm.goToSlide(1)
    const nextButton = wrapper.find('.nav-btn--next')

    // Next button should be disabled if no project type selected
    expect(nextButton.attributes('disabled')).toBeDefined()
  })

  it('should handle transparent background selection', async () => {
    const wrapper = mount(OnboardingSlides, {
      props: {
        isVisible: true,
      },
    })

    // Navigate to background slide
    await wrapper.vm.goToSlide(3)

    // Select transparent option
    const transparentOption = wrapper.find('.transparent-option')
    await transparentOption.trigger('click')

    expect(wrapper.vm.selectedBackground).toBe('transparent')
  })
})
