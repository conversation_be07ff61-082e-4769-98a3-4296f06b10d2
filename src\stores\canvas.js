import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCanvasStore = defineStore('canvas', () => {
  // Canvas dimensions
  const width = ref(800)
  const height = ref(600)

  // Background settings
  const background = ref({
    type: 'color', // 'color', 'gradient', 'image'
    value: '#ffffff',
    colors: ['#ffffff', '#f0f0f0'], // for gradients
    image: null, // for background images
    opacity: 1,
  })

  // Canvas elements
  const elements = ref([])
  const selectedElementId = ref(null)

  // History for undo/redo
  const history = ref([])
  const historyIndex = ref(-1)
  const maxHistorySize = 50

  // Computed properties
  const selectedElement = computed(() => {
    return elements.value.find((el) => el.id === selectedElementId.value) || null
  })

  const canUndo = computed(() => historyIndex.value > 0)
  const canRedo = computed(() => historyIndex.value < history.value.length - 1)

  // Initialize canvas with configuration
  const initializeCanvas = (config) => {
    width.value = config.width
    height.value = config.height

    // Set background
    if (config.background === 'transparent') {
      background.value = {
        type: 'transparent',
        value: 'transparent',
        colors: ['#ffffff', '#f0f0f0'],
        image: null,
        opacity: 0,
      }
    } else if (
      config.background.startsWith('linear-gradient') ||
      config.background.startsWith('radial-gradient')
    ) {
      background.value = {
        type: 'gradient',
        value: config.background,
        colors: ['#667eea', '#764ba2'], // default gradient colors
        image: null,
        opacity: 1,
      }
    } else {
      background.value = {
        type: 'color',
        value: config.background,
        colors: ['#ffffff', '#f0f0f0'],
        image: null,
        opacity: 1,
      }
    }

    // Clear elements and history
    elements.value = []
    selectedElementId.value = null
    history.value = []
    historyIndex.value = -1

    // Save initial state
    saveToHistory()
  }

  // Generate unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Save state to history
  const saveToHistory = () => {
    const state = {
      elements: JSON.parse(JSON.stringify(elements.value)),
      background: JSON.parse(JSON.stringify(background.value)),
    }

    // Remove future history if we're not at the end
    if (historyIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, historyIndex.value + 1)
    }

    history.value.push(state)
    historyIndex.value = history.value.length - 1

    // Limit history size
    if (history.value.length > maxHistorySize) {
      history.value.shift()
      historyIndex.value--
    }
  }

  // Set canvas dimensions
  const setDimensions = (w, h) => {
    width.value = w
    height.value = h
  }

  // Background methods
  const setBackgroundColor = (color) => {
    background.value = {
      type: 'color',
      value: color,
      opacity: 1,
    }
    saveToHistory()
  }

  const setBackgroundGradient = (colors) => {
    background.value = {
      type: 'gradient',
      colors: colors,
      opacity: 1,
    }
    saveToHistory()
  }

  const setBackgroundImage = (image) => {
    background.value = {
      type: 'image',
      image: image,
      opacity: 1,
    }
    saveToHistory()
  }

  const setBackground = (value) => {
    if (value === 'transparent') {
      background.value = {
        type: 'transparent',
        value: 'transparent',
        opacity: 0,
      }
    } else if (value.startsWith('linear-gradient') || value.startsWith('radial-gradient')) {
      background.value = {
        type: 'gradient',
        value: value,
        opacity: 1,
      }
    } else {
      background.value = {
        type: 'color',
        value: value,
        opacity: 1,
      }
    }
    saveToHistory()
  }

  // Helper function to calculate text dimensions
  const calculateTextDimensions = (
    text,
    fontSize,
    fontFamily,
    fontWeight = 'normal',
    fontStyle = 'normal',
  ) => {
    // Create a temporary canvas to measure text
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    ctx.font = `${fontWeight} ${fontStyle} ${fontSize}px ${fontFamily}`

    const metrics = ctx.measureText(text)
    const width = Math.ceil(metrics.width) + 20 // Add some padding
    const height = Math.ceil(fontSize * 1.2) + 10 // Line height + padding

    return { width, height }
  }

  // Element methods
  const addElement = (elementData) => {
    const element = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      ...elementData,
    }

    elements.value.push(element)
    selectedElement.value = element
    saveToHistory()
    return element
  }

  const addTextElement = (options = {}) => {
    const text = options.text || 'New Text'
    const fontSize = options.fontSize || 24
    const fontFamily = options.fontFamily || 'Arial, sans-serif'
    const fontWeight = options.fontWeight || 'normal'
    const fontStyle = options.fontStyle || 'normal'

    // Calculate text dimensions if not provided
    const dimensions =
      options.width && options.height
        ? { width: options.width, height: options.height }
        : calculateTextDimensions(text, fontSize, fontFamily, fontWeight, fontStyle)

    const element = {
      id: generateId(),
      type: 'text',
      text,
      x: options.x || 50,
      y: options.y || 50,
      width: dimensions.width,
      height: dimensions.height,
      fontSize,
      fontFamily,
      color: options.color || '#000000',
      textAlign: options.textAlign || 'left',
      fontWeight,
      fontStyle,
      strokeColor: options.strokeColor || '',
      strokeWidth: options.strokeWidth || 0,
      shadowColor: options.shadowColor || '',
      shadowBlur: options.shadowBlur || 0,
      shadowOffsetX: options.shadowOffsetX || 0,
      shadowOffsetY: options.shadowOffsetY || 0,
      rotation: options.rotation || 0,
      scaleX: options.scaleX || 1,
      scaleY: options.scaleY || 1,
      opacity: options.opacity || 1,
      zIndex: elements.value.length,
    }

    elements.value.push(element)
    selectedElementId.value = element.id
    saveToHistory()

    return element
  }

  const addStickerElement = (options = {}) => {
    const element = {
      id: generateId(),
      type: 'sticker',
      image: options.image,
      x: options.x || 50,
      y: options.y || 50,
      width: options.width || 100,
      height: options.height || 100,
      rotation: options.rotation || 0,
      scaleX: options.scaleX || 1,
      scaleY: options.scaleY || 1,
      opacity: options.opacity || 1,
      zIndex: elements.value.length,
    }

    elements.value.push(element)
    selectedElementId.value = element.id
    saveToHistory()

    return element
  }

  const updateElement = (id, updates) => {
    const index = elements.value.findIndex((el) => el.id === id)
    if (index !== -1) {
      const element = elements.value[index]
      const updatedElement = { ...element, ...updates }

      // If it's a text element and text/font properties changed, recalculate dimensions
      if (element.type === 'text') {
        const textChanged = updates.text !== undefined
        const fontChanged =
          updates.fontSize !== undefined ||
          updates.fontFamily !== undefined ||
          updates.fontWeight !== undefined ||
          updates.fontStyle !== undefined

        if (textChanged || fontChanged) {
          const dimensions = calculateTextDimensions(
            updatedElement.text,
            updatedElement.fontSize,
            updatedElement.fontFamily,
            updatedElement.fontWeight,
            updatedElement.fontStyle,
          )
          updatedElement.width = dimensions.width
          updatedElement.height = dimensions.height
        }
      }

      elements.value[index] = updatedElement
      saveToHistory()
    }
  }

  const deleteElement = (id) => {
    const index = elements.value.findIndex((el) => el.id === id)
    if (index !== -1) {
      elements.value.splice(index, 1)
      if (selectedElementId.value === id) {
        selectedElementId.value = null
      }
      saveToHistory()
    }
  }

  const selectElement = (id) => {
    selectedElementId.value = id
  }

  const duplicateElement = (id) => {
    const element = elements.value.find((el) => el.id === id)
    if (element) {
      const duplicate = {
        ...element,
        id: generateId(),
        x: element.x + 20,
        y: element.y + 20,
        zIndex: elements.value.length,
      }
      elements.value.push(duplicate)
      selectedElementId.value = duplicate.id
      saveToHistory()
      return duplicate
    }
  }

  const moveElementToFront = (id) => {
    const element = elements.value.find((el) => el.id === id)
    if (element) {
      element.zIndex = Math.max(...elements.value.map((el) => el.zIndex)) + 1
      saveToHistory()
    }
  }

  const moveElementToBack = (id) => {
    const element = elements.value.find((el) => el.id === id)
    if (element) {
      element.zIndex = Math.min(...elements.value.map((el) => el.zIndex)) - 1
      saveToHistory()
    }
  }

  // History methods
  const undo = () => {
    if (canUndo.value) {
      historyIndex.value--
      const state = history.value[historyIndex.value]
      elements.value = JSON.parse(JSON.stringify(state.elements))
      background.value = JSON.parse(JSON.stringify(state.background))
      selectedElementId.value = null
    }
  }

  const redo = () => {
    if (canRedo.value) {
      historyIndex.value++
      const state = history.value[historyIndex.value]
      elements.value = JSON.parse(JSON.stringify(state.elements))
      background.value = JSON.parse(JSON.stringify(state.background))
      selectedElementId.value = null
    }
  }

  // Clear canvas
  const clear = () => {
    elements.value = []
    selectedElementId.value = null
    background.value = {
      type: 'color',
      value: '#ffffff',
      opacity: 1,
    }
    saveToHistory()
  }

  // Export/Import
  const exportCanvas = () => {
    return {
      width: width.value,
      height: height.value,
      background: background.value,
      elements: elements.value,
    }
  }

  const importCanvas = (data) => {
    if (data.width) width.value = data.width
    if (data.height) height.value = data.height
    if (data.background) background.value = data.background
    if (data.elements) elements.value = data.elements
    selectedElementId.value = null
    saveToHistory()
  }

  // Template methods
  const saveAsTemplate = (name) => {
    const template = {
      id: generateId(),
      name: name,
      thumbnail: null, // Will be set by component
      data: exportCanvas(),
      createdAt: new Date().toISOString(),
    }

    // Save to localStorage
    const templates = JSON.parse(localStorage.getItem('post-generator-templates') || '[]')
    templates.push(template)
    localStorage.setItem('post-generator-templates', JSON.stringify(templates))

    return template
  }

  const loadTemplate = (template) => {
    importCanvas(template.data)
  }

  const getTemplates = () => {
    return JSON.parse(localStorage.getItem('post-generator-templates') || '[]')
  }

  const deleteTemplate = (id) => {
    const templates = getTemplates()
    const filtered = templates.filter((t) => t.id !== id)
    localStorage.setItem('post-generator-templates', JSON.stringify(filtered))
  }

  // Initialize with empty state
  saveToHistory()

  return {
    // State
    width,
    height,
    background,
    elements,
    selectedElementId,
    history,
    historyIndex,

    // Computed
    selectedElement,
    canUndo,
    canRedo,

    // Methods
    initializeCanvas,
    setDimensions,
    setBackground,
    setBackgroundColor,
    setBackgroundGradient,
    setBackgroundImage,
    addElement,
    addTextElement,
    addStickerElement,
    updateElement,
    deleteElement,
    selectElement,
    duplicateElement,
    moveElementToFront,
    moveElementToBack,
    undo,
    redo,
    clear,
    exportCanvas,
    importCanvas,
    saveAsTemplate,
    loadTemplate,
    getTemplates,
    deleteTemplate,
    saveToHistory,
  }
})
